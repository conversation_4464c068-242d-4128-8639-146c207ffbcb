#!/usr/bin/env python3
"""从日志文件中提取GAN模型参数量统计

此脚本从各个组件的日志文件中提取参数量信息，并生成详细的参数量报告。
"""

import re
import os
from pathlib import Path
from typing import Dict, List, Tu<PERSON>


def extract_params_from_log(log_file: Path) -> Dict[str, int]:
    """从单个日志文件中提取参数量信息"""
    params_info = {}

    if not log_file.exists():
        return params_info

    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 匹配参数量的正则表达式
        patterns = [
            r'参数数量[：:]\s*([0-9,]+)',
            r'参数量[：:]\s*([0-9,]+)',
            r'parameters[：:]\s*([0-9,]+)',
            r'params[：:]\s*([0-9,]+)',
            r'参数:\s*G\(([0-9,]+)\)\s*D\(([0-9,]+)\)',  # GAN模型特殊格式
            r'- 参数数量:\s*([0-9,]+)',  # 模块信息格式
        ]

        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                if 'G\\(' in pattern and 'D\\(' in pattern:
                    # GAN模型格式：G(生成器参数) D(判别器参数)
                    for match in matches:
                        params_info['generator'] = int(match[0].replace(',', ''))
                        params_info['discriminator'] = int(match[1].replace(',', ''))
                else:
                    # 普通格式
                    for match in matches:
                        param_count = int(match.replace(',', ''))
                        component_name = log_file.stem.replace('src_', '').lower()
                        params_info[component_name] = param_count
                break

    except Exception as e:
        print(f"读取日志文件 {log_file} 时出错: {e}")

    return params_info


def extract_all_params() -> Dict[str, Dict[str, int]]:
    """从所有相关日志文件中提取参数量信息"""
    logs_dir = Path("logs")
    all_params = {}

    # 相关的日志文件
    relevant_logs = [
        "src_GANModel.log",
        "src_TimeSeriesGenerator.log",
        "src_TimeSeriesDiscriminator.log",
        "src_SequenceGenerator.log",
        "src_NoiseProcessor.log",
        "src_NoiseEncoder.log",
        "src_DynamicFeatureFusion.log",
        "src_FeatureEncoder.log",
        "src_MultiLayerAttention.log",
        "src_TemporalCoherence.log",
        "src_OutputProjection.log",
        "src_TrendConsistencyBranch.log",
        "src_FeatureCorrelationBranch.log",
        "src_TemporalPatternBranch.log",
        "src_TemporalMultiHeadWrapper.log",
    ]

    for log_name in relevant_logs:
        log_file = logs_dir / log_name
        params = extract_params_from_log(log_file)
        if params:
            component_name = log_name.replace('src_', '').replace('.log', '')
            all_params[component_name] = params

    return all_params


def generate_report(all_params: Dict[str, Dict[str, int]]) -> str:
    """生成参数量统计报告"""
    report = []
    report.append("="*80)
    report.append("GAN模型参数量统计报告")
    report.append("="*80)
    report.append("")

    # 从GANModel日志中提取总体信息
    if 'GANModel' in all_params:
        gan_params = all_params['GANModel']
        if 'generator' in gan_params and 'discriminator' in gan_params:
            generator_total = gan_params['generator']
            discriminator_total = gan_params['discriminator']
            total_params = generator_total + discriminator_total

            report.append("总体统计:")
            report.append(f"  生成器总参数量: {generator_total:,}")
            report.append(f"  判别器总参数量: {discriminator_total:,}")
            report.append(f"  模型总参数量: {total_params:,}")
            report.append(f"  生成器/判别器参数比例: {generator_total/discriminator_total:.2f}:1")
            report.append(f"  判别器/生成器参数比例: {discriminator_total/generator_total:.2f}:1")
            report.append("")

    # 生成器组件详细分解
    report.append("生成器组件参数量分解:")
    report.append("-" * 40)

    generator_components = {
        'NoiseProcessor': 'noise_processor',
        'NoiseEncoder': 'noise_encoder',
        'DynamicFeatureFusion': 'dynamic_feature_fusion',
        'SequenceGenerator': 'sequence_generator',
        'FeatureEncoder': 'feature_encoder',
        'MultiLayerAttention': 'multi_layer_attention',
        'TemporalCoherence': 'temporal_coherence',
        'OutputProjection': 'output_projection',
    }

    generator_total_from_components = 0
    for component, key in generator_components.items():
        if component in all_params:
            params = all_params[component]
            if key in params:
                param_count = params[key]
                generator_total_from_components += param_count
                report.append(f"  {component}: {param_count:,}")
            elif len(params) == 1:
                # 如果只有一个参数值，使用它
                param_count = list(params.values())[0]
                generator_total_from_components += param_count
                report.append(f"  {component}: {param_count:,}")

    report.append("")

    # 判别器组件详细分解
    report.append("判别器组件参数量分解:")
    report.append("-" * 40)

    discriminator_components = {
        'TimeSeriesDiscriminator': 'discriminator',
        'TrendConsistencyBranch': 'trend_consistency_branch',
        'FeatureCorrelationBranch': 'feature_correlation_branch',
        'TemporalPatternBranch': 'temporal_pattern_branch',
        'TemporalMultiHeadWrapper': 'temporal_multi_head_wrapper',
    }

    discriminator_total_from_components = 0
    for component, key in discriminator_components.items():
        if component in all_params:
            params = all_params[component]
            if key in params:
                param_count = params[key]
                discriminator_total_from_components += param_count
                report.append(f"  {component}: {param_count:,}")
            elif len(params) == 1:
                # 如果只有一个参数值，使用它
                param_count = list(params.values())[0]
                discriminator_total_from_components += param_count
                report.append(f"  {component}: {param_count:,}")

    report.append("")

    # 详细组件信息
    report.append("所有检测到的组件参数量:")
    report.append("-" * 40)
    for component, params in all_params.items():
        report.append(f"{component}:")
        for key, value in params.items():
            report.append(f"  {key}: {value:,}")
        report.append("")

    return "\n".join(report)


def main():
    """主函数"""
    print("正在从日志文件中提取参数量信息...")

    all_params = extract_all_params()

    if not all_params:
        print("未找到任何参数量信息。请确保日志文件存在且包含参数量统计。")
        return

    report = generate_report(all_params)
    print(report)

    # 保存报告到文件
    with open("gan_params_report.txt", "w", encoding="utf-8") as f:
        f.write(report)

    print(f"\n报告已保存到: gan_params_report.txt")


if __name__ == "__main__":
    main()
